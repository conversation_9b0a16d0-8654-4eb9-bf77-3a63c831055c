# Arien AI CLI Development Environment Setup Script (PowerShell)
# This script sets up the development environment for the Arien AI CLI

param(
    [switch]$SkipGitHooks,
    [switch]$SkipVSCode,
    [switch]$SkipGlobalTools
)

Write-Host "🚀 Setting up Arien AI CLI development environment..." -ForegroundColor Green

# Check if Node.js is installed
try {
    $nodeVersion = node --version
    Write-Host "✅ Node.js $nodeVersion detected" -ForegroundColor Green
} catch {
    Write-Host "❌ Node.js is not installed. Please install Node.js 18+ and try again." -ForegroundColor Red
    exit 1
}

# Check if npm is installed
try {
    $npmVersion = npm --version
    Write-Host "✅ npm $npmVersion detected" -ForegroundColor Green
} catch {
    Write-Host "❌ npm is not installed. Please install npm and try again." -ForegroundColor Red
    exit 1
}

# Install dependencies
Write-Host "📦 Installing dependencies..." -ForegroundColor Yellow
npm install
if ($LASTEXITCODE -ne 0) {
    Write-Host "❌ Failed to install dependencies" -ForegroundColor Red
    exit 1
}

# Install global development tools (optional)
if (-not $SkipGlobalTools) {
    Write-Host "🔧 Installing global development tools..." -ForegroundColor Yellow
    npm install -g typescript ts-node tsx prettier eslint
}

# Create necessary directories
Write-Host "📁 Creating necessary directories..." -ForegroundColor Yellow
$directories = @("logs", "coverage", "dist")
foreach ($dir in $directories) {
    if (-not (Test-Path $dir)) {
        New-Item -ItemType Directory -Path $dir -Force | Out-Null
    }
}

# Set up Git hooks (if .git exists and not skipped)
if ((Test-Path ".git") -and (-not $SkipGitHooks)) {
    Write-Host "🪝 Setting up Git hooks..." -ForegroundColor Yellow
    
    # Create hooks directory if it doesn't exist
    if (-not (Test-Path ".git/hooks")) {
        New-Item -ItemType Directory -Path ".git/hooks" -Force | Out-Null
    }
    
    # Pre-commit hook
    $preCommitHook = @'
#!/bin/bash
echo "🔍 Running pre-commit checks..."

# Run type checking
npm run type-check
if [ $? -ne 0 ]; then
    echo "❌ Type checking failed"
    exit 1
fi

# Run linting
npm run lint
if [ $? -ne 0 ]; then
    echo "❌ Linting failed"
    exit 1
fi

# Run formatting check
npm run format:check
if [ $? -ne 0 ]; then
    echo "❌ Code formatting check failed. Run 'npm run format' to fix."
    exit 1
fi

echo "✅ Pre-commit checks passed"
'@
    
    $preCommitHook | Out-File -FilePath ".git/hooks/pre-commit" -Encoding UTF8
    
    # Pre-push hook
    $prePushHook = @'
#!/bin/bash
echo "🧪 Running pre-push checks..."

# Run tests
npm run test:ci
if [ $? -ne 0 ]; then
    echo "❌ Tests failed"
    exit 1
fi

echo "✅ Pre-push checks passed"
'@
    
    $prePushHook | Out-File -FilePath ".git/hooks/pre-push" -Encoding UTF8
    
    Write-Host "✅ Git hooks installed" -ForegroundColor Green
}

# Create VS Code settings (if .vscode doesn't exist and not skipped)
if ((-not (Test-Path ".vscode")) -and (-not $SkipVSCode)) {
    Write-Host "⚙️ Creating VS Code settings..." -ForegroundColor Yellow
    New-Item -ItemType Directory -Path ".vscode" -Force | Out-Null
    
    $vsCodeSettings = @'
{
  "typescript.preferences.importModuleSpecifier": "relative",
  "typescript.suggest.autoImports": true,
  "editor.formatOnSave": true,
  "editor.defaultFormatter": "esbenp.prettier-vscode",
  "editor.codeActionsOnSave": {
    "source.fixAll.eslint": true,
    "source.organizeImports": true
  },
  "files.exclude": {
    "**/node_modules": true,
    "**/dist": true,
    "**/coverage": true,
    "**/.tsbuildinfo": true
  },
  "search.exclude": {
    "**/node_modules": true,
    "**/dist": true,
    "**/coverage": true
  },
  "typescript.preferences.includePackageJsonAutoImports": "on",
  "jest.autoRun": "watch",
  "jest.showCoverageOnLoad": true
}
'@
    
    $vsCodeSettings | Out-File -FilePath ".vscode/settings.json" -Encoding UTF8
    
    $vsCodeExtensions = @'
{
  "recommendations": [
    "esbenp.prettier-vscode",
    "dbaeumer.vscode-eslint",
    "ms-vscode.vscode-typescript-next",
    "orta.vscode-jest",
    "bradlc.vscode-tailwindcss",
    "ms-vscode.vscode-json"
  ]
}
'@
    
    $vsCodeExtensions | Out-File -FilePath ".vscode/extensions.json" -Encoding UTF8
    
    $vsCodeLaunch = @'
{
  "version": "0.2.0",
  "configurations": [
    {
      "name": "Debug Arien CLI",
      "type": "node",
      "request": "launch",
      "program": "${workspaceFolder}/src/index.ts",
      "outFiles": ["${workspaceFolder}/dist/**/*.js"],
      "runtimeArgs": ["--loader", "tsx/esm"],
      "env": {
        "NODE_ENV": "development"
      },
      "console": "integratedTerminal",
      "skipFiles": ["<node_internals>/**"]
    },
    {
      "name": "Debug Jest Tests",
      "type": "node",
      "request": "launch",
      "program": "${workspaceFolder}/node_modules/.bin/jest",
      "args": ["--runInBand", "--no-cache"],
      "console": "integratedTerminal",
      "internalConsoleOptions": "neverOpen",
      "env": {
        "NODE_ENV": "test"
      }
    }
  ]
}
'@
    
    $vsCodeLaunch | Out-File -FilePath ".vscode/launch.json" -Encoding UTF8
    
    Write-Host "✅ VS Code settings created" -ForegroundColor Green
}

# Run initial build and tests
Write-Host "🏗️ Running initial build..." -ForegroundColor Yellow
npm run build
if ($LASTEXITCODE -ne 0) {
    Write-Host "❌ Build failed" -ForegroundColor Red
    exit 1
}

Write-Host "🧪 Running tests..." -ForegroundColor Yellow
npm run test
if ($LASTEXITCODE -ne 0) {
    Write-Host "⚠️ Some tests failed, but continuing..." -ForegroundColor Yellow
}

Write-Host ""
Write-Host "🎉 Development environment setup complete!" -ForegroundColor Green
Write-Host ""
Write-Host "📋 Available commands:" -ForegroundColor Cyan
Write-Host "  npm run dev          - Start development server" -ForegroundColor White
Write-Host "  npm run build        - Build the project" -ForegroundColor White
Write-Host "  npm run test         - Run tests" -ForegroundColor White
Write-Host "  npm run test:watch   - Run tests in watch mode" -ForegroundColor White
Write-Host "  npm run lint         - Lint code" -ForegroundColor White
Write-Host "  npm run format       - Format code" -ForegroundColor White
Write-Host "  npm run validate     - Run all checks" -ForegroundColor White
Write-Host ""
Write-Host "🚀 You're ready to start developing!" -ForegroundColor Green
Write-Host "   Run 'npm run dev' to start the CLI in development mode" -ForegroundColor White
