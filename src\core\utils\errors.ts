/**
 * @license
 * Copyright 2025 Arien LLC
 * License: MIT
 */

import { getLogger } from './logger.js';

const logger = getLogger('ErrorHandler');

/**
 * Base error class for Arien AI CLI
 */
export abstract class ArienError extends Error {
  public readonly code: string;
  public readonly context?: Record<string, any>;
  public readonly timestamp: Date;

  constructor(message: string, code: string, context?: Record<string, any>) {
    super(message);
    this.name = this.constructor.name;
    this.code = code;
    this.context = context;
    this.timestamp = new Date();

    // Ensure proper prototype chain
    Object.setPrototypeOf(this, new.target.prototype);

    // Capture stack trace
    if (Error.captureStackTrace) {
      Error.captureStackTrace(this, this.constructor);
    }
  }

  /**
   * Convert error to JSON for logging/serialization
   */
  toJSON(): Record<string, any> {
    return {
      name: this.name,
      message: this.message,
      code: this.code,
      context: this.context,
      timestamp: this.timestamp.toISOString(),
      stack: this.stack
    };
  }
}

/**
 * Authentication related errors
 */
export class AuthenticationError extends ArienError {
  constructor(message: string, context?: Record<string, any>) {
    super(message, 'AUTH_ERROR', context);
  }
}

/**
 * Provider related errors
 */
export class ProviderError extends ArienError {
  constructor(message: string, providerId?: string, context?: Record<string, any>) {
    super(message, 'PROVIDER_ERROR', { ...context, providerId });
  }
}

/**
 * Configuration related errors
 */
export class ConfigurationError extends ArienError {
  constructor(message: string, context?: Record<string, any>) {
    super(message, 'CONFIG_ERROR', context);
  }
}

/**
 * Validation related errors
 */
export class ValidationError extends ArienError {
  public readonly errors: string[];

  constructor(message: string, errors: string[], context?: Record<string, any>) {
    super(message, 'VALIDATION_ERROR', context);
    this.errors = errors;
  }

  toJSON(): Record<string, any> {
    return {
      ...super.toJSON(),
      errors: this.errors
    };
  }
}

/**
 * Function execution related errors
 */
export class FunctionExecutionError extends ArienError {
  constructor(message: string, functionName?: string, context?: Record<string, any>) {
    super(message, 'FUNCTION_ERROR', { ...context, functionName });
  }
}

/**
 * Network/API related errors
 */
export class NetworkError extends ArienError {
  public readonly statusCode?: number;
  public readonly response?: any;

  constructor(message: string, statusCode?: number, response?: any, context?: Record<string, any>) {
    super(message, 'NETWORK_ERROR', context);
    this.statusCode = statusCode;
    this.response = response;
  }

  toJSON(): Record<string, any> {
    return {
      ...super.toJSON(),
      statusCode: this.statusCode,
      response: this.response
    };
  }
}

/**
 * Chat/LLM related errors
 */
export class ChatError extends ArienError {
  constructor(message: string, context?: Record<string, any>) {
    super(message, 'CHAT_ERROR', context);
  }
}

/**
 * File system related errors
 */
export class FileSystemError extends ArienError {
  constructor(message: string, path?: string, context?: Record<string, any>) {
    super(message, 'FS_ERROR', { ...context, path });
  }
}

/**
 * Error handler utility class
 */
export class ErrorHandler {
  /**
   * Handle and log an error appropriately
   */
  static handle(error: Error | ArienError, context?: Record<string, any>): void {
    if (error instanceof ArienError) {
      logger.error(
        `${error.name}: ${error.message}`,
        error,
        { ...error.context, ...context }
      );
    } else {
      logger.error(
        `Unexpected error: ${error.message}`,
        error,
        context
      );
    }
  }

  /**
   * Wrap a function with error handling
   */
  static wrap<T extends (...args: any[]) => any>(
    fn: T,
    context?: Record<string, any>
  ): T {
    return ((...args: any[]) => {
      try {
        const result = fn(...args);
        
        // Handle async functions
        if (result instanceof Promise) {
          return result.catch((error) => {
            ErrorHandler.handle(error, context);
            throw error;
          });
        }
        
        return result;
      } catch (error) {
        ErrorHandler.handle(error as Error, context);
        throw error;
      }
    }) as T;
  }

  /**
   * Create a safe version of an async function that doesn't throw
   */
  static safe<T extends (...args: any[]) => Promise<any>>(
    fn: T,
    defaultValue?: any
  ): (...args: Parameters<T>) => Promise<ReturnType<T> | typeof defaultValue> {
    return async (...args: Parameters<T>) => {
      try {
        return await fn(...args);
      } catch (error) {
        ErrorHandler.handle(error as Error);
        return defaultValue;
      }
    };
  }

  /**
   * Retry a function with exponential backoff
   */
  static async retry<T>(
    fn: () => Promise<T>,
    options: {
      maxAttempts?: number;
      baseDelay?: number;
      maxDelay?: number;
      backoffFactor?: number;
    } = {}
  ): Promise<T> {
    const {
      maxAttempts = 3,
      baseDelay = 1000,
      maxDelay = 10000,
      backoffFactor = 2
    } = options;

    let lastError: Error;
    
    for (let attempt = 1; attempt <= maxAttempts; attempt++) {
      try {
        return await fn();
      } catch (error) {
        lastError = error as Error;
        
        if (attempt === maxAttempts) {
          break;
        }

        const delay = Math.min(
          baseDelay * Math.pow(backoffFactor, attempt - 1),
          maxDelay
        );

        logger.warn(
          `Attempt ${attempt} failed, retrying in ${delay}ms`,
          undefined,
          { error: lastError.message, attempt, maxAttempts }
        );

        await new Promise(resolve => setTimeout(resolve, delay));
      }
    }

    throw lastError!;
  }

  /**
   * Convert unknown error to ArienError
   */
  static normalize(error: unknown, defaultMessage = 'An unexpected error occurred'): ArienError {
    if (error instanceof ArienError) {
      return error;
    }

    if (error instanceof Error) {
      return new ArienError(error.message, 'UNKNOWN_ERROR', {
        originalName: error.name,
        originalStack: error.stack
      });
    }

    if (typeof error === 'string') {
      return new ArienError(error, 'UNKNOWN_ERROR');
    }

    return new ArienError(defaultMessage, 'UNKNOWN_ERROR', {
      originalError: String(error)
    });
  }

  /**
   * Check if error is recoverable
   */
  static isRecoverable(error: Error | ArienError): boolean {
    if (error instanceof NetworkError) {
      // Network errors are often recoverable
      return true;
    }

    if (error instanceof ValidationError) {
      // Validation errors are not recoverable without user input
      return false;
    }

    if (error instanceof AuthenticationError) {
      // Auth errors require user intervention
      return false;
    }

    if (error instanceof ConfigurationError) {
      // Config errors require user intervention
      return false;
    }

    // Default to recoverable for unknown errors
    return true;
  }

  /**
   * Get user-friendly error message
   */
  static getUserMessage(error: Error | ArienError): string {
    if (error instanceof AuthenticationError) {
      return 'Authentication failed. Please check your credentials and try again.';
    }

    if (error instanceof NetworkError) {
      return 'Network error occurred. Please check your internet connection and try again.';
    }

    if (error instanceof ValidationError) {
      return `Validation failed: ${error.errors.join(', ')}`;
    }

    if (error instanceof ProviderError) {
      return `Provider error: ${error.message}`;
    }

    if (error instanceof ConfigurationError) {
      return `Configuration error: ${error.message}`;
    }

    if (error instanceof FunctionExecutionError) {
      return `Function execution failed: ${error.message}`;
    }

    if (error instanceof ChatError) {
      return `Chat error: ${error.message}`;
    }

    if (error instanceof FileSystemError) {
      return `File system error: ${error.message}`;
    }

    // Default message for unknown errors
    return error.message || 'An unexpected error occurred. Please try again.';
  }
}
