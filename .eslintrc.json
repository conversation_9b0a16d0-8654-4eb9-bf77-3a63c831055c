{"env": {"node": true, "es2022": true, "jest": true}, "extends": ["eslint:recommended", "@typescript-eslint/recommended", "@typescript-eslint/recommended-requiring-type-checking"], "parser": "@typescript-eslint/parser", "parserOptions": {"ecmaVersion": 2022, "sourceType": "module", "project": "./tsconfig.json", "ecmaFeatures": {"jsx": true}}, "plugins": ["@typescript-eslint", "react", "react-hooks"], "rules": {"@typescript-eslint/no-unused-vars": ["error", {"argsIgnorePattern": "^_"}], "@typescript-eslint/explicit-function-return-type": "off", "@typescript-eslint/explicit-module-boundary-types": "off", "@typescript-eslint/no-explicit-any": "warn", "@typescript-eslint/no-non-null-assertion": "warn", "@typescript-eslint/prefer-const": "error", "@typescript-eslint/no-var-requires": "error", "@typescript-eslint/ban-ts-comment": "warn", "@typescript-eslint/no-empty-function": "warn", "@typescript-eslint/no-inferrable-types": "off", "@typescript-eslint/consistent-type-imports": "error", "@typescript-eslint/consistent-type-definitions": ["error", "interface"], "@typescript-eslint/prefer-nullish-coalescing": "error", "@typescript-eslint/prefer-optional-chain": "error", "@typescript-eslint/no-unnecessary-condition": "warn", "@typescript-eslint/no-floating-promises": "error", "@typescript-eslint/await-thenable": "error", "@typescript-eslint/require-await": "warn", "react/jsx-uses-react": "off", "react/react-in-jsx-scope": "off", "react-hooks/rules-of-hooks": "error", "react-hooks/exhaustive-deps": "warn", "no-console": "warn", "no-debugger": "error", "no-alert": "error", "no-var": "error", "prefer-const": "error", "prefer-arrow-callback": "error", "arrow-spacing": "error", "comma-dangle": ["error", "never"], "semi": ["error", "always"], "quotes": ["error", "single", {"avoidEscape": true}], "indent": ["error", 2, {"SwitchCase": 1}], "max-len": ["warn", {"code": 120, "ignoreUrls": true, "ignoreStrings": true}], "object-curly-spacing": ["error", "always"], "array-bracket-spacing": ["error", "never"], "eol-last": ["error", "always"], "no-trailing-spaces": "error", "no-multiple-empty-lines": ["error", {"max": 2, "maxEOF": 1}]}, "settings": {"react": {"version": "detect"}}, "overrides": [{"files": ["**/*.test.ts", "**/*.test.tsx", "**/*.spec.ts", "**/*.spec.tsx"], "env": {"jest": true}, "rules": {"@typescript-eslint/no-explicit-any": "off", "@typescript-eslint/no-non-null-assertion": "off", "no-console": "off"}}, {"files": ["jest.config.js", "*.config.js"], "env": {"node": true}, "rules": {"@typescript-eslint/no-var-requires": "off"}}], "ignorePatterns": ["dist/", "node_modules/", "coverage/", "*.d.ts"]}