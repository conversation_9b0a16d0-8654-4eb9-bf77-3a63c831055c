/**
 * @license
 * Copyright 2025 Arien LLC
 * License: MIT
 */

import { ValidationError } from './errors.js';
import { AuthConfig, LLMProvider, ChatMessage, BuiltinFunction } from '../types/index.js';

/**
 * Validation result interface
 */
export interface ValidationResult {
  isValid: boolean;
  errors: string[];
  warnings?: string[];
}

/**
 * Core validation utilities
 */
export class Validator {
  /**
   * Validate authentication configuration
   */
  static validateAuthConfig(config: AuthConfig): ValidationResult {
    const errors: string[] = [];
    const warnings: string[] = [];

    if (!config.providerId || typeof config.providerId !== 'string') {
      errors.push('Provider ID is required and must be a string');
    }

    // Check if at least one authentication method is provided
    const hasApiKey = config.apiKey && config.apiKey.trim().length > 0;
    const hasOAuth = config.accessToken && config.accessToken.trim().length > 0;

    if (!hasApiKey && !hasOAuth) {
      errors.push('Either API key or OAuth tokens are required');
    }

    // Validate API key if provided
    if (hasApiKey) {
      const apiKeyValidation = Validator.validateApiKey(config.apiKey!, config.providerId);
      errors.push(...apiKeyValidation.errors);
      if (apiKeyValidation.warnings) {
        warnings.push(...apiKeyValidation.warnings);
      }
    }

    // Validate OAuth tokens if provided
    if (hasOAuth) {
      if (config.expiresAt && config.expiresAt < Date.now()) {
        errors.push('OAuth token has expired');
      }

      if (!config.refreshToken) {
        warnings.push('No refresh token provided - token cannot be automatically renewed');
      }
    }

    return { isValid: errors.length === 0, errors, warnings };
  }

  /**
   * Validate API key format
   */
  static validateApiKey(apiKey: string, providerId: string): ValidationResult {
    const errors: string[] = [];
    const warnings: string[] = [];

    if (!apiKey || typeof apiKey !== 'string') {
      errors.push('API key is required and must be a string');
      return { isValid: false, errors, warnings };
    }

    const trimmedKey = apiKey.trim();
    if (trimmedKey.length === 0) {
      errors.push('API key cannot be empty');
      return { isValid: false, errors, warnings };
    }

    if (apiKey !== trimmedKey) {
      errors.push('API key should not have leading or trailing whitespace');
    }

    if (apiKey.includes(' ')) {
      errors.push('API key should not contain spaces');
    }

    // Provider-specific validation
    switch (providerId.toLowerCase()) {
      case 'openai':
        if (!apiKey.startsWith('sk-')) {
          errors.push('OpenAI API key should start with "sk-"');
        }
        if (apiKey.length < 20) {
          errors.push('OpenAI API key appears to be too short');
        }
        break;

      case 'anthropic':
        if (!apiKey.startsWith('sk-ant-')) {
          errors.push('Anthropic API key should start with "sk-ant-"');
        }
        break;

      case 'google':
        if (apiKey.length < 20) {
          warnings.push('Google API key appears to be short, please verify it is correct');
        }
        break;

      case 'deepseek':
        if (!apiKey.startsWith('sk-')) {
          warnings.push('DeepSeek API key typically starts with "sk-"');
        }
        break;
    }

    return { isValid: errors.length === 0, errors, warnings };
  }

  /**
   * Validate chat message
   */
  static validateChatMessage(message: ChatMessage): ValidationResult {
    const errors: string[] = [];
    const warnings: string[] = [];

    if (!message.id || typeof message.id !== 'string') {
      errors.push('Message ID is required and must be a string');
    }

    if (!message.role || !['user', 'assistant', 'system'].includes(message.role)) {
      errors.push('Message role must be one of: user, assistant, system');
    }

    if (!message.content || typeof message.content !== 'string') {
      errors.push('Message content is required and must be a string');
    } else {
      const contentValidation = Validator.validateMessageContent(message.content);
      errors.push(...contentValidation.errors);
      if (contentValidation.warnings) {
        warnings.push(...contentValidation.warnings);
      }
    }

    if (!message.timestamp || !(message.timestamp instanceof Date)) {
      errors.push('Message timestamp is required and must be a Date object');
    }

    return { isValid: errors.length === 0, errors, warnings };
  }

  /**
   * Validate message content
   */
  static validateMessageContent(content: string): ValidationResult {
    const errors: string[] = [];
    const warnings: string[] = [];

    if (!content || typeof content !== 'string') {
      errors.push('Message content is required and must be a string');
      return { isValid: false, errors, warnings };
    }

    if (content.trim().length === 0) {
      errors.push('Message content cannot be empty');
      return { isValid: false, errors, warnings };
    }

    // Check message length
    if (content.length > 100000) {
      errors.push('Message content is too long (max 100,000 characters)');
    } else if (content.length > 50000) {
      warnings.push('Message content is very long and may be truncated');
    }

    // Check for potentially problematic content
    if (content.includes('\0')) {
      errors.push('Message content contains null characters');
    }

    // Check for excessive whitespace
    const lines = content.split('\n');
    if (lines.length > 1000) {
      warnings.push('Message content has many line breaks');
    }

    return { isValid: errors.length === 0, errors, warnings };
  }

  /**
   * Validate provider configuration
   */
  static validateProvider(provider: LLMProvider): ValidationResult {
    const errors: string[] = [];
    const warnings: string[] = [];

    if (!provider.id || typeof provider.id !== 'string') {
      errors.push('Provider ID is required and must be a string');
    }

    if (!provider.name || typeof provider.name !== 'string') {
      errors.push('Provider name is required and must be a string');
    }

    if (!Array.isArray(provider.models) || provider.models.length === 0) {
      errors.push('Provider must have at least one model');
    } else {
      provider.models.forEach((model, index) => {
        if (!model.id || typeof model.id !== 'string') {
          errors.push(`Model ${index + 1} ID is required and must be a string`);
        }
        if (!model.name || typeof model.name !== 'string') {
          errors.push(`Model ${index + 1} name is required and must be a string`);
        }
        if (typeof model.maxTokens !== 'number' || model.maxTokens <= 0) {
          errors.push(`Model ${index + 1} must have a valid maxTokens value`);
        }
        if (typeof model.supportsFunctionCalling !== 'boolean') {
          errors.push(`Model ${index + 1} supportsFunctionCalling must be a boolean`);
        }
      });
    }

    if (!Array.isArray(provider.configFields)) {
      errors.push('Provider config fields must be an array');
    }

    if (typeof provider.requiresAuth !== 'boolean') {
      errors.push('Provider requiresAuth must be a boolean');
    }

    if (!['api_key', 'oauth', 'both'].includes(provider.authType)) {
      errors.push('Provider authType must be one of: api_key, oauth, both');
    }

    return { isValid: errors.length === 0, errors, warnings };
  }

  /**
   * Validate function definition
   */
  static validateFunction(func: BuiltinFunction): ValidationResult {
    const errors: string[] = [];
    const warnings: string[] = [];

    if (!func.name || typeof func.name !== 'string') {
      errors.push('Function name is required and must be a string');
    }

    if (!func.description || typeof func.description !== 'string') {
      errors.push('Function description is required and must be a string');
    }

    if (!func.parameters || typeof func.parameters !== 'object') {
      errors.push('Function parameters are required and must be an object');
    } else {
      if (func.parameters.type !== 'object') {
        errors.push('Function parameters type must be "object"');
      }

      if (!func.parameters.properties || typeof func.parameters.properties !== 'object') {
        errors.push('Function parameters must have properties object');
      }

      if (!Array.isArray(func.parameters.required)) {
        errors.push('Function parameters required must be an array');
      }
    }

    if (typeof func.handler !== 'function') {
      errors.push('Function handler must be a function');
    }

    return { isValid: errors.length === 0, errors, warnings };
  }

  /**
   * Validate function call arguments
   */
  static validateFunctionArgs(
    args: Record<string, any>,
    parameters: BuiltinFunction['parameters']
  ): ValidationResult {
    const errors: string[] = [];
    const warnings: string[] = [];

    // Check required parameters
    for (const requiredParam of parameters.required) {
      if (!(requiredParam in args)) {
        errors.push(`Missing required parameter: ${requiredParam}`);
      }
    }

    // Validate parameter types
    for (const [key, value] of Object.entries(args)) {
      const paramSchema = parameters.properties[key];
      if (paramSchema) {
        const typeValidation = Validator.validateParameterType(key, value, paramSchema);
        errors.push(...typeValidation.errors);
        if (typeValidation.warnings) {
          warnings.push(...typeValidation.warnings);
        }
      } else {
        warnings.push(`Unknown parameter: ${key}`);
      }
    }

    return { isValid: errors.length === 0, errors, warnings };
  }

  /**
   * Validate parameter type
   */
  private static validateParameterType(
    key: string,
    value: any,
    schema: { type: string; enum?: string[] }
  ): ValidationResult {
    const errors: string[] = [];
    const warnings: string[] = [];

    const expectedType = schema.type;
    const actualType = typeof value;

    switch (expectedType) {
      case 'string':
        if (actualType !== 'string') {
          errors.push(`Parameter '${key}' must be a string`);
        } else if (schema.enum && !schema.enum.includes(value)) {
          errors.push(`Parameter '${key}' must be one of: ${schema.enum.join(', ')}`);
        }
        break;

      case 'number':
        if (actualType !== 'number') {
          errors.push(`Parameter '${key}' must be a number`);
        } else if (isNaN(value) || !isFinite(value)) {
          errors.push(`Parameter '${key}' must be a valid number`);
        }
        break;

      case 'boolean':
        if (actualType !== 'boolean') {
          errors.push(`Parameter '${key}' must be a boolean`);
        }
        break;

      case 'array':
        if (!Array.isArray(value)) {
          errors.push(`Parameter '${key}' must be an array`);
        }
        break;

      case 'object':
        if (actualType !== 'object' || Array.isArray(value) || value === null) {
          errors.push(`Parameter '${key}' must be an object`);
        }
        break;

      default:
        warnings.push(`Unknown type '${expectedType}' for parameter '${key}'`);
    }

    return { isValid: errors.length === 0, errors, warnings };
  }

  /**
   * Throw ValidationError if validation fails
   */
  static assert(validation: ValidationResult, message?: string): void {
    if (!validation.isValid) {
      throw new ValidationError(
        message || 'Validation failed',
        validation.errors
      );
    }
  }
}
