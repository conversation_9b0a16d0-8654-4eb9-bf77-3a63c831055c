{"compilerOptions": {"target": "ES2022", "lib": ["ES2022"], "module": "ES2022", "moduleResolution": "bundler", "outDir": "./dist", "rootDir": "./src", "strict": true, "noImplicitAny": true, "strictNullChecks": true, "strictFunctionTypes": true, "noImplicitReturns": true, "noFallthroughCasesInSwitch": true, "noUncheckedIndexedAccess": true, "exactOptionalPropertyTypes": true, "esModuleInterop": true, "allowSyntheticDefaultImports": true, "skipLibCheck": true, "forceConsistentCasingInFileNames": true, "declaration": true, "declarationMap": true, "sourceMap": true, "removeComments": false, "resolveJsonModule": true, "jsx": "react-jsx", "types": ["node", "jest"], "baseUrl": "./src", "paths": {"@/*": ["./*"], "@cli/*": ["./cli/*"], "@core/*": ["./core/*"]}, "incremental": true, "tsBuildInfoFile": "./dist/.tsbuildinfo"}, "include": ["src/**/*"], "exclude": ["node_modules", "dist", "coverage", "**/*.test.ts", "**/*.spec.ts"], "ts-node": {"esm": true, "experimentalSpecifierResolution": "node"}}