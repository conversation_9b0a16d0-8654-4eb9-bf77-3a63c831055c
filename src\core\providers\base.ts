/**
 * @license
 * Copyright 2025 Arien LLC
 * License: MIT
 */

import { ChatMessage, LLMResponse, FunctionCall, BuiltinFunction } from '../types/index.js';
import { getLogger } from '../utils/logger.js';
import { <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, ProviderError, FunctionExecutionError } from '../utils/errors.js';
import { Validator } from '../utils/validation.js';

export interface LLMProviderConfig {
  apiKey?: string;
  baseUrl?: string;
  organization?: string;
  projectId?: string;
  accessToken?: string;
  [key: string]: any;
}

export abstract class BaseLLMProvider {
  protected config: LLMProviderConfig;
  protected builtinFunctions: BuiltinFunction[] = [];
  protected logger = getLogger('Provider');

  constructor(config: LLMProviderConfig) {
    this.config = config;
    this.logger.debug('Provider initialized', { config: this.sanitizeConfig(config) });
  }

  abstract get providerId(): string;
  abstract get providerName(): string;

  abstract chat(
    messages: ChatMessage[],
    model: string,
    options?: {
      temperature?: number;
      maxTokens?: number;
      systemPrompt?: string;
      functions?: BuiltinFunction[];
    }
  ): Promise<LLMResponse>;

  abstract validateConfig(): Promise<boolean>;

  setBuiltinFunctions(functions: BuiltinFunction[]): void {
    this.builtinFunctions = functions;
  }

  protected async executeFunctionCall(functionCall: FunctionCall): Promise<any> {
    const func = this.builtinFunctions.find(f => f.name === functionCall.name);
    if (!func) {
      const error = new FunctionExecutionError(
        `Unknown function: ${functionCall.name}`,
        functionCall.name,
        { availableFunctions: this.builtinFunctions.map(f => f.name) }
      );
      this.logger.error('Function not found', error);
      throw error;
    }

    // Validate function arguments
    const validation = Validator.validateFunctionArgs(functionCall.arguments, func.parameters);
    if (!validation.isValid) {
      const error = new FunctionExecutionError(
        `Function argument validation failed: ${validation.errors.join(', ')}`,
        functionCall.name,
        { arguments: functionCall.arguments, errors: validation.errors }
      );
      this.logger.error('Function argument validation failed', error);
      throw error;
    }

    try {
      this.logger.debug('Executing function', {
        functionName: functionCall.name,
        arguments: functionCall.arguments
      });

      const result = await func.handler(functionCall.arguments);

      this.logger.debug('Function executed successfully', {
        functionName: functionCall.name,
        resultType: typeof result
      });

      return result;
    } catch (error) {
      const functionError = new FunctionExecutionError(
        `Function execution failed: ${error instanceof Error ? error.message : String(error)}`,
        functionCall.name,
        { arguments: functionCall.arguments, originalError: error }
      );
      this.logger.error('Function execution failed', functionError);
      throw functionError;
    }
  }

  protected formatMessagesForProvider(messages: ChatMessage[], systemPrompt?: string): any[] {
    const formattedMessages: any[] = [];

    // Add system message if provided
    if (systemPrompt) {
      formattedMessages.push({
        role: 'system',
        content: systemPrompt
      });
    }

    // Add conversation messages
    for (const message of messages) {
      formattedMessages.push({
        role: message.role,
        content: message.content
      });
    }

    return formattedMessages;
  }

  protected formatFunctionsForProvider(functions: BuiltinFunction[]): any[] {
    return functions.map(func => ({
      name: func.name,
      description: func.description,
      parameters: func.parameters
    }));
  }

  protected createChatMessage(
    role: 'user' | 'assistant' | 'system',
    content: string,
    metadata?: any
  ): ChatMessage {
    return {
      id: this.generateId(),
      role,
      content,
      timestamp: new Date(),
      metadata
    };
  }

  protected generateId(): string {
    return Math.random().toString(36).substring(2) + Date.now().toString(36);
  }

  protected calculateTokens(text: string): number {
    // Rough estimation: 1 token ≈ 4 characters for English text
    return Math.ceil(text.length / 4);
  }

  /**
   * Sanitize config for logging (remove sensitive data)
   */
  protected sanitizeConfig(config: LLMProviderConfig): Record<string, any> {
    const sanitized = { ...config };

    // Remove or mask sensitive fields
    if (sanitized.apiKey) {
      sanitized.apiKey = `${sanitized.apiKey.substring(0, 8)}...`;
    }
    if (sanitized.accessToken) {
      sanitized.accessToken = `${sanitized.accessToken.substring(0, 8)}...`;
    }

    return sanitized;
  }

  /**
   * Validate provider configuration
   */
  protected validateProviderConfig(): void {
    if (!this.config) {
      throw new ProviderError('Provider configuration is required', this.providerId);
    }

    // Basic validation - subclasses can override for specific validation
    if (!this.config.apiKey && !this.config.accessToken) {
      throw new ProviderError(
        'Either API key or access token is required',
        this.providerId,
        { config: this.sanitizeConfig(this.config) }
      );
    }
  }

  /**
   * Handle provider errors with proper logging
   */
  protected handleError(error: unknown, context?: Record<string, any>): never {
    const normalizedError = ErrorHandler.normalize(error);

    if (normalizedError instanceof ProviderError) {
      this.logger.error('Provider error', normalizedError, context);
      throw normalizedError;
    }

    const providerError = new ProviderError(
      normalizedError.message,
      this.providerId,
      { ...context, originalError: normalizedError }
    );

    this.logger.error('Provider error', providerError, context);
    throw providerError;
  }
}
